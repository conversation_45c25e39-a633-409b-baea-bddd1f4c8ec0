"use client";

import { useState } from "react";
import Image from "next/image";

const processSteps = [
  {
    step: 1,
    title: "Discover & Analyze",
    description: "We audit your existing workflows, tools, and customer data to uncover inefficiencies and automation opportunities. Every system is mapped for clarity.",
    image: "https://framerusercontent.com/images/LMV9IYKI2TkgMh5KmQhbeIV2A.png"
  },
  {
    step: 2,
    title: "Build & Deploy",
    description: "Our team designs and implements custom AI solutions tailored to your specific needs. We ensure seamless integration with your existing tools and workflows.",
    image: "https://framerusercontent.com/images/LMV9IYKI2TkgMh5KmQhbeIV2A.png"
  },
  {
    step: 3,
    title: "Monitor & Optimize",
    description: "We continuously monitor performance, gather insights, and optimize your automation systems to ensure maximum efficiency and ROI.",
    image: "https://framerusercontent.com/images/LMV9IYKI2TkgMh5KmQhbeIV2A.png"
  }
];

export default function Process() {
  const [activeStep, setActiveStep] = useState(1);

  return (
    <section id="process" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <span className="text-primary-600 font-semibold text-sm uppercase tracking-wide mb-4 block">
              PROCESS
            </span>
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Our Simple & Smart Process
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to collaborate, create, and scale, all in one place.
            </p>
          </div>

          {/* Process Steps Navigation */}
          <div className="flex justify-center mb-12">
            <div className="flex items-center space-x-4 bg-white rounded-full p-2 shadow-lg">
              {processSteps.map((step) => (
                <button
                  key={step.step}
                  onClick={() => setActiveStep(step.step)}
                  className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                    activeStep === step.step
                      ? 'bg-gradient-to-r from-primary-500 to-accent-500 text-white shadow-lg'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  STEP {step.step}
                </button>
              ))}
            </div>
          </div>

          {/* Active Step Content */}
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
            <div className="grid lg:grid-cols-2 gap-0">
              {/* Image */}
              <div className="relative h-64 lg:h-auto bg-gradient-to-br from-primary-100 to-accent-100">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-32 h-32 bg-gradient-to-br from-primary-500 to-accent-500 rounded-full flex items-center justify-center">
                    <span className="text-4xl font-bold text-white">
                      {String(activeStep).padStart(2, '0')}
                    </span>
                  </div>
                </div>
                {/* Decorative elements */}
                <div className="absolute top-4 left-4 w-16 h-16 bg-white/20 rounded-full"></div>
                <div className="absolute bottom-4 right-4 w-24 h-24 bg-white/10 rounded-full"></div>
                <div className="absolute top-1/2 left-1/4 w-8 h-8 bg-white/30 rounded-full"></div>
              </div>

              {/* Content */}
              <div className="p-8 lg:p-12 flex flex-col justify-center">
                <div className="mb-6">
                  <span className="text-6xl font-bold text-primary-500 opacity-20">
                    {String(activeStep).padStart(2, '0')}
                  </span>
                </div>
                
                <h3 className="text-3xl font-bold text-gray-900 mb-6">
                  {processSteps[activeStep - 1].title}
                </h3>
                
                <p className="text-lg text-gray-600 leading-relaxed mb-8">
                  {processSteps[activeStep - 1].description}
                </p>

                {/* Progress indicators */}
                <div className="flex space-x-2">
                  {processSteps.map((step) => (
                    <div
                      key={step.step}
                      className={`h-2 rounded-full transition-all duration-300 ${
                        step.step === activeStep
                          ? 'w-12 bg-gradient-to-r from-primary-500 to-accent-500'
                          : step.step < activeStep
                          ? 'w-8 bg-primary-300'
                          : 'w-8 bg-gray-200'
                      }`}
                    ></div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* All Steps Overview */}
          <div className="mt-16 grid md:grid-cols-3 gap-8">
            {processSteps.map((step) => (
              <div
                key={step.step}
                className={`group cursor-pointer transition-all duration-300 ${
                  activeStep === step.step ? 'scale-105' : 'hover:scale-102'
                }`}
                onClick={() => setActiveStep(step.step)}
              >
                <div className="bg-white rounded-xl p-6 border border-gray-100 hover:border-primary-200 transition-all duration-300">
                  <div className="flex items-center mb-4">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm ${
                      activeStep === step.step
                        ? 'bg-gradient-to-r from-primary-500 to-accent-500 text-white'
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {String(step.step).padStart(2, '0')}
                    </div>
                    <h4 className="ml-3 font-bold text-gray-900">
                      {step.title}
                    </h4>
                  </div>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
