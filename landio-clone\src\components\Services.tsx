"use client";

import { useState } from "react";

const services = [
  {
    id: "workflows",
    title: "Automated Workflows",
    subtitle: "Automate repetitive tasks",
    description: "We help you streamline internal operations by automating manual workflows",
    features: [
      "Cost Management",
      "Payment reminder",
      "Employee Tracking",
      "Social media post",
      "Automate repetitive tasks"
    ],
    fullDescription: "Boost efficiency across teams with smart automation Build intelligent workflows that automate multi-step processes across tools and platforms"
  },
  {
    id: "intelligence",
    title: "Real-Time Intelligence",
    subtitle: "Research anything...",
    description: "Make smarter decisions with live data insights Tap into real-time data",
    features: [
      "Research",
      "Software & App Industry",
      "UX & UI Design Industry",
      "High Converting Customer"
    ],
    fullDescription: "Access accurate, real-time data to drive smarter decisions and uncover actionable insights"
  },
  {
    id: "ai-agents",
    title: "Custom AI Agent development",
    subtitle: "Code",
    description: "We develop custom AI agents that integrate seamlessly with your tools",
    features: [],
    code: `class AutomationAgent:
    def __init__(self, activation_limit):
        self.activation_limit = activation_limit
        self.current_mode = "idle"
    
    def evaluate_task(self, workload_value):
        if workload_value > self.activation_limit:
            self.current_mode = "engaged"
            return "Automation agent has been successfully activated!"
        else:
            return "No activation needed. Agent stays idle."

    def get_current_mode(self):
        return f"Current operational mode: {self.current_mode}"`
  },
  {
    id: "consulting",
    title: "AI Strategy Consulting",
    subtitle: "Expert guidance",
    description: "Get expert guidance to implement AI solutions that drive business growth",
    features: [
      "Strategic Planning",
      "Implementation Roadmap",
      "Performance Optimization",
      "ROI Analysis"
    ],
    fullDescription: "Transform your business with comprehensive AI strategy consulting and implementation support"
  }
];

export default function Services() {
  const [activeService, setActiveService] = useState("workflows");

  return (
    <section id="services" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <span className="text-primary-600 font-semibold text-sm uppercase tracking-wide mb-4 block">
              SERVICES
            </span>
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Smarter Services, Built with AI
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to automate operations, boost productivity
            </p>
          </div>

          {/* Services Grid */}
          <div className="grid lg:grid-cols-2 gap-8">
            {services.map((service, index) => (
              <div
                key={service.id}
                className={`group relative bg-white rounded-2xl p-8 border transition-all duration-300 card-hover ${
                  activeService === service.id 
                    ? 'border-primary-200 shadow-xl' 
                    : 'border-gray-100 hover:border-primary-100'
                }`}
                onMouseEnter={() => setActiveService(service.id)}
              >
                {/* Background decoration */}
                <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-primary-50 to-accent-50 rounded-full opacity-50 transform translate-x-12 -translate-y-12 group-hover:scale-110 transition-transform duration-300"></div>
                
                <div className="relative z-10">
                  {/* Service Header */}
                  <div className="mb-6">
                    <span className="text-primary-600 font-medium text-sm uppercase tracking-wide">
                      {service.subtitle}
                    </span>
                    <h3 className="text-2xl font-bold text-gray-900 mt-2 mb-4">
                      {service.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {service.description}
                    </p>
                  </div>

                  {/* Features List */}
                  {service.features.length > 0 && (
                    <div className="mb-6">
                      <div className="flex flex-wrap gap-2">
                        {service.features.map((feature, idx) => (
                          <span
                            key={idx}
                            className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Code Block for AI Agents */}
                  {service.code && (
                    <div className="mb-6">
                      <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                        <div className="flex items-center mb-3">
                          <div className="flex space-x-2">
                            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          </div>
                          <span className="ml-4 text-gray-400 text-sm">Python</span>
                        </div>
                        <pre className="text-green-400 text-sm leading-relaxed overflow-x-auto">
                          <code>{service.code}</code>
                        </pre>
                      </div>
                    </div>
                  )}

                  {/* Full Description */}
                  {service.fullDescription && (
                    <p className="text-gray-600 leading-relaxed mb-6">
                      {service.fullDescription}
                    </p>
                  )}

                  {/* Learn More Button */}
                  <button className="inline-flex items-center text-primary-600 font-medium hover:text-primary-700 transition-colors duration-200">
                    Learn More
                    <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
