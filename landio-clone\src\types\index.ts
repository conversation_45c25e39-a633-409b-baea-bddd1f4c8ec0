export interface NavItem {
  label: string;
  href: string;
  isExternal?: boolean;
}

export interface Feature {
  title: string;
  description: string;
  icon?: string;
  image?: string;
}

export interface Service {
  title: string;
  description: string;
  features: string[];
  image?: string;
  code?: string;
}

export interface Testimonial {
  name: string;
  role: string;
  company: string;
  content: string;
  avatar: string;
  rating?: number;
}

export interface CaseStudy {
  name: string;
  title: string;
  description: string;
  metrics: {
    label: string;
    value: string;
  }[];
  image: string;
  quote?: string;
}

export interface PricingPlan {
  name: string;
  price: string;
  period: string;
  description?: string;
  features: string[];
  isPopular?: boolean;
  buttonText: string;
  buttonLink: string;
}

export interface TeamMember {
  name: string;
  role: string;
  image: string;
  social: {
    twitter?: string;
    linkedin?: string;
    instagram?: string;
  };
}

export interface FAQ {
  question: string;
  answer: string;
}

export interface ProcessStep {
  step: number;
  title: string;
  description: string;
  image: string;
}

export interface Integration {
  name: string;
  logo: string;
  category?: string;
}
