import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Landio - AI Agency & Landing Page Template",
  description: "AI Automation for Modern Businesses Made Simple. Automate Smarter. Grow Faster. With AI.",
  keywords: "AI automation, business automation, AI agency, landing page, workflow automation",
  authors: [{ name: "Landio" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="font-sans antialiased">
        {children}
      </body>
    </html>
  );
}
