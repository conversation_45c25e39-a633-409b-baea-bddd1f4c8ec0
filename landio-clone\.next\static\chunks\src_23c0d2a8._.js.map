{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Boldbat/Webs/share_wallet/landio-clone/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function scrollToSection(sectionId: string) {\n  const element = document.getElementById(sectionId);\n  if (element) {\n    element.scrollIntoView({ behavior: \"smooth\" });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,gBAAgB,SAAiB;IAC/C,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,QAAQ,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9C;AACF", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Boldbat/Webs/share_wallet/landio-clone/src/components/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { cn } from \"@/lib/utils\";\nimport type { NavItem } from \"@/types\";\n\nconst navItems: NavItem[] = [\n  { label: \"Services\", href: \"#services\" },\n  { label: \"Process\", href: \"#process\" },\n  { label: \"Pricing\", href: \"#pricing\" },\n  { label: \"Blog\", href: \"/blog\" },\n  { label: \"Contact\", href: \"/contact\" },\n];\n\nexport default function Header() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  const handleNavClick = (href: string) => {\n    if (href.startsWith(\"#\")) {\n      const element = document.querySelector(href);\n      if (element) {\n        element.scrollIntoView({ behavior: \"smooth\" });\n      }\n    }\n    setIsMobileMenuOpen(false);\n  };\n\n  return (\n    <header\n      className={cn(\n        \"fixed top-0 left-0 right-0 z-50 transition-all duration-300\",\n        isScrolled\n          ? \"bg-white/95 backdrop-blur-md shadow-sm border-b border-gray-100\"\n          : \"bg-transparent\"\n      )}\n    >\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-primary-500 to-accent-500 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">L</span>\n            </div>\n            <span className=\"text-xl font-bold text-gray-900\">Landio</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <button\n                key={item.label}\n                onClick={() => handleNavClick(item.href)}\n                className=\"text-gray-600 hover:text-gray-900 font-medium transition-colors duration-200\"\n              >\n                {item.label}\n              </button>\n            ))}\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <Link\n              href=\"https://framer.link/D4dc7gs\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-2.5 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200 btn-hover\"\n            >\n              Get Template\n            </Link>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200\"\n            aria-label=\"Toggle mobile menu\"\n          >\n            <svg\n              className=\"w-6 h-6\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              {isMobileMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"lg:hidden border-t border-gray-100 bg-white/95 backdrop-blur-md\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1\">\n              {navItems.map((item) => (\n                <button\n                  key={item.label}\n                  onClick={() => handleNavClick(item.href)}\n                  className=\"block w-full text-left px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md font-medium transition-colors duration-200\"\n                >\n                  {item.label}\n                </button>\n              ))}\n              <div className=\"pt-4 pb-2\">\n                <Link\n                  href=\"https://framer.link/D4dc7gs\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"block w-full text-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-2.5 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                >\n                  Get Template\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAOA,MAAM,WAAsB;IAC1B;QAAE,OAAO;QAAY,MAAM;IAAY;IACvC;QAAE,OAAO;QAAW,MAAM;IAAW;IACrC;QAAE,OAAO;QAAW,MAAM;IAAW;IACrC;QAAE,OAAO;QAAQ,MAAM;IAAQ;IAC/B;QAAE,OAAO;QAAW,MAAM;IAAW;CACtC;AAEc,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,IAAI,SAAS;gBACX,QAAQ,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAC9C;QACF;QACA,oBAAoB;IACtB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,oEACA;kBAGN,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;oCAEC,SAAS,IAAM,eAAe,KAAK,IAAI;oCACvC,WAAU;8CAET,KAAK,KAAK;mCAJN,KAAK,KAAK;;;;;;;;;;sCAUrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;0CACX;;;;;;;;;;;sCAMH,6LAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;4BACV,cAAW;sCAEX,cAAA,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;0CAEP,iCACC,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;yDAGJ,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAQX,kCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;oCAEC,SAAS,IAAM,eAAe,KAAK,IAAI;oCACvC,WAAU;8CAET,KAAK,KAAK;mCAJN,KAAK,KAAK;;;;;0CAOnB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAhIwB;KAAA", "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Boldbat/Webs/share_wallet/landio-clone/src/components/Hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { useState, useEffect } from \"react\";\n\nexport default function Hero() {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    setIsVisible(true);\n  }, []);\n\n  return (\n    <section id=\"hero\" className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-50 via-white to-blue-50\">\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-primary-200 to-primary-300 rounded-full opacity-20 animate-pulse\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-accent-200 to-accent-300 rounded-full opacity-20 animate-pulse delay-1000\"></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-primary-100 to-accent-100 rounded-full opacity-30 animate-ping delay-2000\"></div>\n      </div>\n\n      {/* Grid Pattern */}\n      <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <div className=\"text-center max-w-5xl mx-auto\">\n          {/* Badge */}\n          <div className={`inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-primary-100 to-accent-100 text-primary-700 text-sm font-medium mb-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>\n            <span className=\"w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse\"></span>\n            NEW GEN AI AUTOMATION PARTNER\n          </div>\n\n          {/* Main Headline */}\n          <h1 className={`text-4xl sm:text-5xl lg:text-7xl font-bold text-gray-900 mb-6 leading-tight transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n            <span className=\"block\">Automate Smarter.</span>\n            <span className=\"block\">Grow Faster.</span>\n            <span className=\"block gradient-text\">With AI.</span>\n          </h1>\n\n          {/* Subtitle */}\n          <p className={`text-xl sm:text-2xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed transition-all duration-1000 delay-400 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n            AI Automation for Modern Businesses Made Simple\n          </p>\n\n          {/* CTA Button */}\n          <div className={`mb-16 transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n            <Link\n              href=\"https://framer.link/D4dc7gs\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white text-lg font-semibold rounded-xl hover:from-primary-600 hover:to-primary-700 transition-all duration-300 btn-hover shadow-lg hover:shadow-xl\"\n            >\n              Book A Free Call\n              <svg className=\"ml-2 w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n              </svg>\n            </Link>\n          </div>\n\n          {/* Social Media Links */}\n          <div className={`flex items-center justify-center space-x-6 transition-all duration-1000 delay-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n            <Link\n              href=\"https://x.com/home\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center text-gray-600 hover:text-primary-500 hover:shadow-lg transition-all duration-200\"\n              aria-label=\"Twitter\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"/>\n              </svg>\n            </Link>\n            <Link\n              href=\"https://www.instagram.com/\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center text-gray-600 hover:text-primary-500 hover:shadow-lg transition-all duration-200\"\n              aria-label=\"Instagram\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.875-.875 2.026-1.297 3.323-1.297s2.448.422 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.781c-.49 0-.928-.422-.928-.928 0-.49.438-.928.928-.928.49 0 .928.438.928.928 0 .506-.438.928-.928.928zm-3.832 9.244c-1.297 0-2.448-.49-3.323-1.297-.928-.807-1.418-1.958-1.418-3.255s.49-2.448 1.418-3.323c.875-.875 2.026-1.297 3.323-1.297s2.448.422 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.255c-.875.807-2.026 1.297-3.323 1.297z\"/>\n              </svg>\n            </Link>\n            <Link\n              href=\"https://www.facebook.com/\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center text-gray-600 hover:text-primary-500 hover:shadow-lg transition-all duration-200\"\n              aria-label=\"Facebook\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n              </svg>\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <div className=\"w-6 h-10 border-2 border-gray-300 rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-gray-300 rounded-full mt-2 animate-pulse\"></div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,aAAa;QACf;yBAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,IAAG;QAAO,WAAU;;0BAE3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAW,AAAC,0KAA6O,OAApE,YAAY,8BAA8B;;8CAClO,6LAAC;oCAAK,WAAU;;;;;;gCAAgE;;;;;;;sCAKlF,6LAAC;4BAAG,WAAW,AAAC,sHAAyL,OAApE,YAAY,8BAA8B;;8CAC7K,6LAAC;oCAAK,WAAU;8CAAQ;;;;;;8CACxB,6LAAC;oCAAK,WAAU;8CAAQ;;;;;;8CACxB,6LAAC;oCAAK,WAAU;8CAAsB;;;;;;;;;;;;sCAIxC,6LAAC;4BAAE,WAAW,AAAC,oHAAuL,OAApE,YAAY,8BAA8B;sCAA6B;;;;;;sCAKzM,6LAAC;4BAAI,WAAW,AAAC,gDAAmH,OAApE,YAAY,8BAA8B;sCACxG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;;oCACX;kDAEC,6LAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAM3E,6LAAC;4BAAI,WAAW,AAAC,qFAAwJ,OAApE,YAAY,8BAA8B;;8CAC7I,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,6LAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAGZ,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,6LAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAGZ,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,6LAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB;GArGwB;KAAA", "debugId": null}}, {"offset": {"line": 594, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Boldbat/Webs/share_wallet/landio-clone/src/components/FounderNote.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from \"next/image\";\n\nexport default function FounderNote() {\n  return (\n    <section id=\"founder-note\" className=\"py-20 bg-gradient-to-r from-gray-50 to-blue-50\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-12\">\n            <span className=\"text-primary-600 font-semibold text-sm uppercase tracking-wide\">\n              WE ANALYZE YOUR DATA\n            </span>\n          </div>\n          \n          <div className=\"bg-white rounded-2xl shadow-xl p-8 lg:p-12 relative overflow-hidden\">\n            {/* Background decoration */}\n            <div className=\"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary-100 to-accent-100 rounded-full opacity-50 transform translate-x-16 -translate-y-16\"></div>\n            \n            <div className=\"grid lg:grid-cols-2 gap-12 items-center relative z-10\">\n              {/* Quote */}\n              <div className=\"lg:order-1\">\n                <blockquote className=\"text-2xl lg:text-3xl font-semibold text-gray-900 leading-relaxed mb-8\">\n                  \"We find what to automate, who your users are & how AI can optimize your workflow. Best part is we also build and launch real solutions.\"\n                </blockquote>\n                \n                <div className=\"flex items-center\">\n                  <div className=\"w-16 h-16 rounded-full overflow-hidden mr-4 ring-4 ring-primary-100\">\n                    <Image\n                      src=\"https://framerusercontent.com/images/W7xYkGKzPzvnPv58ZBNzxS3JZI.jpg\"\n                      alt=\"Co-founder & AI Strategy Lead\"\n                      width={64}\n                      height={64}\n                      className=\"w-full h-full object-cover\"\n                    />\n                  </div>\n                  <div>\n                    <p className=\"font-semibold text-gray-900\">Co-founder & AI Strategy Lead</p>\n                    <p className=\"text-gray-600 text-sm\">Landio</p>\n                  </div>\n                </div>\n              </div>\n              \n              {/* Visual Element */}\n              <div className=\"lg:order-2 flex justify-center\">\n                <div className=\"relative\">\n                  <div className=\"w-80 h-80 bg-gradient-to-br from-primary-500 to-accent-500 rounded-full opacity-10 animate-pulse\"></div>\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"w-64 h-64 bg-gradient-to-br from-primary-400 to-accent-400 rounded-full opacity-20 animate-ping\"></div>\n                  </div>\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"w-48 h-48 bg-gradient-to-br from-primary-300 to-accent-300 rounded-full opacity-30\"></div>\n                  </div>\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"w-32 h-32 bg-gradient-to-br from-primary-500 to-accent-500 rounded-full flex items-center justify-center\">\n                      <svg className=\"w-16 h-16 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,6LAAC;QAAQ,IAAG;QAAe,WAAU;kBACnC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAiE;;;;;;;;;;;kCAKnF,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAW,WAAU;0DAAwE;;;;;;0DAI9F,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4DACJ,KAAI;4DACJ,KAAI;4DACJ,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;;;;;;kEAGd,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAA8B;;;;;;0EAC3C,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;kDAM3C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;;;;;;;;;;8DAEjB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;;;;;;;;;;8DAEjB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAuB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY/F;KAhEwB", "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Boldbat/Webs/share_wallet/landio-clone/src/components/Benefits.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from \"next/image\";\n\nconst benefits = [\n  {\n    title: \"Real-Time Intelligence\",\n    description: \"Access accurate, real-time data to drive smarter decisions\",\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n      </svg>\n    )\n  },\n  {\n    title: \"Measurable Impact\",\n    description: \"Track performance, uncover insights, and achieve data-backed growth\",\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n      </svg>\n    )\n  },\n  {\n    title: \"Seamless Integration\",\n    description: \"Connect tools, teams, and workflows with intelligent automation\",\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\" />\n      </svg>\n    ),\n    image: \"https://framerusercontent.com/images/EdYwMQFSY0q3kCvKPrFpiTV5w.png\"\n  }\n];\n\nexport default function Benefits() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <span className=\"text-primary-600 font-semibold text-sm uppercase tracking-wide mb-4 block\">\n              BENEFITS\n            </span>\n            <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n              Why Choose Us?\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Everything you need to automate, optimize, and scale\n            </p>\n          </div>\n\n          {/* Benefits Grid */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {benefits.map((benefit, index) => (\n              <div\n                key={benefit.title}\n                className=\"group relative bg-white rounded-2xl p-8 border border-gray-100 hover:border-primary-200 transition-all duration-300 card-hover\"\n              >\n                {/* Background decoration */}\n                <div className=\"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary-50 to-accent-50 rounded-full opacity-50 transform translate-x-10 -translate-y-10 group-hover:scale-110 transition-transform duration-300\"></div>\n                \n                <div className=\"relative z-10\">\n                  {/* Icon */}\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform duration-300\">\n                    {benefit.icon}\n                  </div>\n\n                  {/* Content */}\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n                    {benefit.title}\n                  </h3>\n                  <p className=\"text-gray-600 leading-relaxed mb-6\">\n                    {benefit.description}\n                  </p>\n\n                  {/* Image for Seamless Integration */}\n                  {benefit.image && (\n                    <div className=\"mt-6\">\n                      <Image\n                        src={benefit.image}\n                        alt={benefit.title}\n                        width={200}\n                        height={120}\n                        className=\"w-full h-auto rounded-lg opacity-80 group-hover:opacity-100 transition-opacity duration-300\"\n                      />\n                    </div>\n                  )}\n\n                  {/* Hover effect arrow */}\n                  <div className=\"absolute bottom-6 right-6 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0 transition-all duration-300\">\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n                    </svg>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,WAAW;IACf;QACE,OAAO;QACP,aAAa;QACb,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAK,GAAE;;;;;;;;;;;IAG7E;IACA;QACE,OAAO;QACP,aAAa;QACb,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAK,GAAE;;;;;;;;;;;IAG7E;IACA;QACE,OAAO;QACP,aAAa;QACb,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAK,GAAE;;;;;;;;;;;QAG3E,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA4E;;;;;;0CAG5F,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gCAEC,WAAU;;kDAGV,6LAAC;wCAAI,WAAU;;;;;;kDAEf,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI;;;;;;0DAIf,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;4CAIrB,QAAQ,KAAK,kBACZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,QAAQ,KAAK;oDAClB,KAAK,QAAQ,KAAK;oDAClB,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;0DAMhB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;+BApCtE,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;AA+ClC;KArEwB", "debugId": null}}, {"offset": {"line": 1087, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Boldbat/Webs/share_wallet/landio-clone/src/components/Services.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\n\nconst services = [\n  {\n    id: \"workflows\",\n    title: \"Automated Workflows\",\n    subtitle: \"Automate repetitive tasks\",\n    description: \"We help you streamline internal operations by automating manual workflows\",\n    features: [\n      \"Cost Management\",\n      \"Payment reminder\",\n      \"Employee Tracking\",\n      \"Social media post\",\n      \"Automate repetitive tasks\"\n    ],\n    fullDescription: \"Boost efficiency across teams with smart automation Build intelligent workflows that automate multi-step processes across tools and platforms\"\n  },\n  {\n    id: \"intelligence\",\n    title: \"Real-Time Intelligence\",\n    subtitle: \"Research anything...\",\n    description: \"Make smarter decisions with live data insights Tap into real-time data\",\n    features: [\n      \"Research\",\n      \"Software & App Industry\",\n      \"UX & UI Design Industry\",\n      \"High Converting Customer\"\n    ],\n    fullDescription: \"Access accurate, real-time data to drive smarter decisions and uncover actionable insights\"\n  },\n  {\n    id: \"ai-agents\",\n    title: \"Custom AI Agent development\",\n    subtitle: \"Code\",\n    description: \"We develop custom AI agents that integrate seamlessly with your tools\",\n    features: [],\n    code: `class AutomationAgent:\n    def __init__(self, activation_limit):\n        self.activation_limit = activation_limit\n        self.current_mode = \"idle\"\n    \n    def evaluate_task(self, workload_value):\n        if workload_value > self.activation_limit:\n            self.current_mode = \"engaged\"\n            return \"Automation agent has been successfully activated!\"\n        else:\n            return \"No activation needed. Agent stays idle.\"\n\n    def get_current_mode(self):\n        return f\"Current operational mode: {self.current_mode}\"`\n  },\n  {\n    id: \"consulting\",\n    title: \"AI Strategy Consulting\",\n    subtitle: \"Expert guidance\",\n    description: \"Get expert guidance to implement AI solutions that drive business growth\",\n    features: [\n      \"Strategic Planning\",\n      \"Implementation Roadmap\",\n      \"Performance Optimization\",\n      \"ROI Analysis\"\n    ],\n    fullDescription: \"Transform your business with comprehensive AI strategy consulting and implementation support\"\n  }\n];\n\nexport default function Services() {\n  const [activeService, setActiveService] = useState(\"workflows\");\n\n  return (\n    <section id=\"services\" className=\"py-20 bg-gradient-to-br from-gray-50 to-blue-50\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <span className=\"text-primary-600 font-semibold text-sm uppercase tracking-wide mb-4 block\">\n              SERVICES\n            </span>\n            <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n              Smarter Services, Built with AI\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Everything you need to automate operations, boost productivity\n            </p>\n          </div>\n\n          {/* Services Grid */}\n          <div className=\"grid lg:grid-cols-2 gap-8\">\n            {services.map((service, index) => (\n              <div\n                key={service.id}\n                className={`group relative bg-white rounded-2xl p-8 border transition-all duration-300 card-hover ${\n                  activeService === service.id \n                    ? 'border-primary-200 shadow-xl' \n                    : 'border-gray-100 hover:border-primary-100'\n                }`}\n                onMouseEnter={() => setActiveService(service.id)}\n              >\n                {/* Background decoration */}\n                <div className=\"absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-primary-50 to-accent-50 rounded-full opacity-50 transform translate-x-12 -translate-y-12 group-hover:scale-110 transition-transform duration-300\"></div>\n                \n                <div className=\"relative z-10\">\n                  {/* Service Header */}\n                  <div className=\"mb-6\">\n                    <span className=\"text-primary-600 font-medium text-sm uppercase tracking-wide\">\n                      {service.subtitle}\n                    </span>\n                    <h3 className=\"text-2xl font-bold text-gray-900 mt-2 mb-4\">\n                      {service.title}\n                    </h3>\n                    <p className=\"text-gray-600 leading-relaxed\">\n                      {service.description}\n                    </p>\n                  </div>\n\n                  {/* Features List */}\n                  {service.features.length > 0 && (\n                    <div className=\"mb-6\">\n                      <div className=\"flex flex-wrap gap-2\">\n                        {service.features.map((feature, idx) => (\n                          <span\n                            key={idx}\n                            className=\"px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full\"\n                          >\n                            {feature}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Code Block for AI Agents */}\n                  {service.code && (\n                    <div className=\"mb-6\">\n                      <div className=\"bg-gray-900 rounded-lg p-4 overflow-x-auto\">\n                        <div className=\"flex items-center mb-3\">\n                          <div className=\"flex space-x-2\">\n                            <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n                            <div className=\"w-3 h-3 bg-yellow-500 rounded-full\"></div>\n                            <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n                          </div>\n                          <span className=\"ml-4 text-gray-400 text-sm\">Python</span>\n                        </div>\n                        <pre className=\"text-green-400 text-sm leading-relaxed overflow-x-auto\">\n                          <code>{service.code}</code>\n                        </pre>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Full Description */}\n                  {service.fullDescription && (\n                    <p className=\"text-gray-600 leading-relaxed mb-6\">\n                      {service.fullDescription}\n                    </p>\n                  )}\n\n                  {/* Learn More Button */}\n                  <button className=\"inline-flex items-center text-primary-600 font-medium hover:text-primary-700 transition-colors duration-200\">\n                    Learn More\n                    <svg className=\"ml-2 w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n                    </svg>\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,iBAAiB;IACnB;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,iBAAiB;IACnB;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU,EAAE;QACZ,MAAO;IAcT;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,iBAAiB;IACnB;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA4E;;;;;;0CAG5F,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gCAEC,WAAW,AAAC,yFAIX,OAHC,kBAAkB,QAAQ,EAAE,GACxB,iCACA;gCAEN,cAAc,IAAM,iBAAiB,QAAQ,EAAE;;kDAG/C,6LAAC;wCAAI,WAAU;;;;;;kDAEf,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ;;;;;;kEAEnB,6LAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,6LAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;;;;;;;4CAKvB,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBACzB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC9B,6LAAC;4DAEC,WAAU;sEAET;2DAHI;;;;;;;;;;;;;;;4CAWd,QAAQ,IAAI,kBACX,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAI,WAAU;;;;;;;;;;;;8EAEjB,6LAAC;oEAAK,WAAU;8EAA6B;;;;;;;;;;;;sEAE/C,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;0EAAM,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;4CAO1B,QAAQ,eAAe,kBACtB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,eAAe;;;;;;0DAK5B,6LAAC;gDAAO,WAAU;;oDAA8G;kEAE9H,6LAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACtE,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;+BAvEtE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAkF/B;GA1GwB;KAAA", "debugId": null}}, {"offset": {"line": 1423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Boldbat/Webs/share_wallet/landio-clone/src/components/Features.tsx"], "sourcesContent": ["\"use client\";\n\nconst features = [\n  {\n    title: \"Workflow Automation\",\n    description: \"Automate complex business processes to boost speed, clarity, and efficiency.\",\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n      </svg>\n    )\n  },\n  {\n    title: \"Custom AI Solutions\",\n    description: \"Build tailored AI systems that align with your business goals and challenges.\",\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n      </svg>\n    )\n  },\n  {\n    title: \"AI Assistant\",\n    description: \"Deploy intelligent virtual agents to streamline tasks.\",\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n      </svg>\n    )\n  },\n  {\n    title: \"Sales & Marketing\",\n    description: \"Leverage AI to optimize campaigns, track leads, and personalize outreach.\",\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n      </svg>\n    )\n  },\n  {\n    title: \"Performance Tracking\",\n    description: \"Track automation results in real time to improve and scale your workflows.\",\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n      </svg>\n    )\n  },\n  {\n    title: \"Seamless Integrations\",\n    description: \"Connect your tools and apps for smooth, unified AI-powered workflows.\",\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\" />\n      </svg>\n    )\n  }\n];\n\nexport default function Features() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <span className=\"text-primary-600 font-semibold text-sm uppercase tracking-wide mb-4 block\">\n              FEATURES\n            </span>\n            <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n              All features in one place\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Everything you need to automate operations, boost productivity\n            </p>\n          </div>\n\n          {/* Features Grid */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {features.map((feature, index) => (\n              <div\n                key={feature.title}\n                className=\"group relative bg-white rounded-2xl p-8 border border-gray-100 hover:border-primary-200 transition-all duration-300 card-hover\"\n              >\n                {/* Background decoration */}\n                <div className=\"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary-50 to-accent-50 rounded-full opacity-50 transform translate-x-10 -translate-y-10 group-hover:scale-110 transition-transform duration-300\"></div>\n                \n                <div className=\"relative z-10\">\n                  {/* Icon */}\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform duration-300\">\n                    {feature.icon}\n                  </div>\n\n                  {/* Content */}\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n                    {feature.title}\n                  </h3>\n                  <p className=\"text-gray-600 leading-relaxed\">\n                    {feature.description}\n                  </p>\n\n                  {/* Hover effect arrow */}\n                  <div className=\"absolute bottom-6 right-6 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0 transition-all duration-300\">\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n                    </svg>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,WAAW;IACf;QACE,OAAO;QACP,aAAa;QACb,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAK,GAAE;;;;;;;;;;;IAG7E;IACA;QACE,OAAO;QACP,aAAa;QACb,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAK,GAAE;;;;;;;;;;;IAG7E;IACA;QACE,OAAO;QACP,aAAa;QACb,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAK,GAAE;;;;;;;;;;;IAG7E;IACA;QACE,OAAO;QACP,aAAa;QACb,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAK,GAAE;;;;;;;;;;;IAG7E;IACA;QACE,OAAO;QACP,aAAa;QACb,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAK,GAAE;;;;;;;;;;;IAG7E;IACA;QACE,OAAO;QACP,aAAa;QACb,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAK,GAAE;;;;;;;;;;;IAG7E;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA4E;;;;;;0CAG5F,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gCAEC,WAAU;;kDAGV,6LAAC;wCAAI,WAAU;;;;;;kDAEf,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI;;;;;;0DAIf,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAItB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;+BAvBtE,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;AAkClC;KAxDwB", "debugId": null}}, {"offset": {"line": 1728, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Boldbat/Webs/share_wallet/landio-clone/src/components/Process.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Image from \"next/image\";\n\nconst processSteps = [\n  {\n    step: 1,\n    title: \"Discover & Analyze\",\n    description: \"We audit your existing workflows, tools, and customer data to uncover inefficiencies and automation opportunities. Every system is mapped for clarity.\",\n    image: \"https://framerusercontent.com/images/LMV9IYKI2TkgMh5KmQhbeIV2A.png\"\n  },\n  {\n    step: 2,\n    title: \"Build & Deploy\",\n    description: \"Our team designs and implements custom AI solutions tailored to your specific needs. We ensure seamless integration with your existing tools and workflows.\",\n    image: \"https://framerusercontent.com/images/LMV9IYKI2TkgMh5KmQhbeIV2A.png\"\n  },\n  {\n    step: 3,\n    title: \"Monitor & Optimize\",\n    description: \"We continuously monitor performance, gather insights, and optimize your automation systems to ensure maximum efficiency and ROI.\",\n    image: \"https://framerusercontent.com/images/LMV9IYKI2TkgMh5KmQhbeIV2A.png\"\n  }\n];\n\nexport default function Process() {\n  const [activeStep, setActiveStep] = useState(1);\n\n  return (\n    <section id=\"process\" className=\"py-20 bg-gradient-to-br from-gray-50 to-blue-50\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <span className=\"text-primary-600 font-semibold text-sm uppercase tracking-wide mb-4 block\">\n              PROCESS\n            </span>\n            <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n              Our Simple & Smart Process\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Everything you need to collaborate, create, and scale, all in one place.\n            </p>\n          </div>\n\n          {/* Process Steps Navigation */}\n          <div className=\"flex justify-center mb-12\">\n            <div className=\"flex items-center space-x-4 bg-white rounded-full p-2 shadow-lg\">\n              {processSteps.map((step) => (\n                <button\n                  key={step.step}\n                  onClick={() => setActiveStep(step.step)}\n                  className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${\n                    activeStep === step.step\n                      ? 'bg-gradient-to-r from-primary-500 to-accent-500 text-white shadow-lg'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                  }`}\n                >\n                  STEP {step.step}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Active Step Content */}\n          <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden\">\n            <div className=\"grid lg:grid-cols-2 gap-0\">\n              {/* Image */}\n              <div className=\"relative h-64 lg:h-auto bg-gradient-to-br from-primary-100 to-accent-100\">\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                  <div className=\"w-32 h-32 bg-gradient-to-br from-primary-500 to-accent-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-4xl font-bold text-white\">\n                      {String(activeStep).padStart(2, '0')}\n                    </span>\n                  </div>\n                </div>\n                {/* Decorative elements */}\n                <div className=\"absolute top-4 left-4 w-16 h-16 bg-white/20 rounded-full\"></div>\n                <div className=\"absolute bottom-4 right-4 w-24 h-24 bg-white/10 rounded-full\"></div>\n                <div className=\"absolute top-1/2 left-1/4 w-8 h-8 bg-white/30 rounded-full\"></div>\n              </div>\n\n              {/* Content */}\n              <div className=\"p-8 lg:p-12 flex flex-col justify-center\">\n                <div className=\"mb-6\">\n                  <span className=\"text-6xl font-bold text-primary-500 opacity-20\">\n                    {String(activeStep).padStart(2, '0')}\n                  </span>\n                </div>\n                \n                <h3 className=\"text-3xl font-bold text-gray-900 mb-6\">\n                  {processSteps[activeStep - 1].title}\n                </h3>\n                \n                <p className=\"text-lg text-gray-600 leading-relaxed mb-8\">\n                  {processSteps[activeStep - 1].description}\n                </p>\n\n                {/* Progress indicators */}\n                <div className=\"flex space-x-2\">\n                  {processSteps.map((step) => (\n                    <div\n                      key={step.step}\n                      className={`h-2 rounded-full transition-all duration-300 ${\n                        step.step === activeStep\n                          ? 'w-12 bg-gradient-to-r from-primary-500 to-accent-500'\n                          : step.step < activeStep\n                          ? 'w-8 bg-primary-300'\n                          : 'w-8 bg-gray-200'\n                      }`}\n                    ></div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* All Steps Overview */}\n          <div className=\"mt-16 grid md:grid-cols-3 gap-8\">\n            {processSteps.map((step) => (\n              <div\n                key={step.step}\n                className={`group cursor-pointer transition-all duration-300 ${\n                  activeStep === step.step ? 'scale-105' : 'hover:scale-102'\n                }`}\n                onClick={() => setActiveStep(step.step)}\n              >\n                <div className=\"bg-white rounded-xl p-6 border border-gray-100 hover:border-primary-200 transition-all duration-300\">\n                  <div className=\"flex items-center mb-4\">\n                    <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm ${\n                      activeStep === step.step\n                        ? 'bg-gradient-to-r from-primary-500 to-accent-500 text-white'\n                        : 'bg-gray-100 text-gray-600'\n                    }`}>\n                      {String(step.step).padStart(2, '0')}\n                    </div>\n                    <h4 className=\"ml-3 font-bold text-gray-900\">\n                      {step.title}\n                    </h4>\n                  </div>\n                  <p className=\"text-gray-600 text-sm leading-relaxed\">\n                    {step.description}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAKA,MAAM,eAAe;IACnB;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA4E;;;;;;0CAG5F,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC;oCAEC,SAAS,IAAM,cAAc,KAAK,IAAI;oCACtC,WAAW,AAAC,kEAIX,OAHC,eAAe,KAAK,IAAI,GACpB,yEACA;;wCAEP;wCACO,KAAK,IAAI;;mCARV,KAAK,IAAI;;;;;;;;;;;;;;;kCAetB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,OAAO,YAAY,QAAQ,CAAC,GAAG;;;;;;;;;;;;;;;;sDAKtC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,OAAO,YAAY,QAAQ,CAAC,GAAG;;;;;;;;;;;sDAIpC,6LAAC;4CAAG,WAAU;sDACX,YAAY,CAAC,aAAa,EAAE,CAAC,KAAK;;;;;;sDAGrC,6LAAC;4CAAE,WAAU;sDACV,YAAY,CAAC,aAAa,EAAE,CAAC,WAAW;;;;;;sDAI3C,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC;oDAEC,WAAW,AAAC,gDAMX,OALC,KAAK,IAAI,KAAK,aACV,yDACA,KAAK,IAAI,GAAG,aACZ,uBACA;mDAND,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAgB1B,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC;gCAEC,WAAW,AAAC,oDAEX,OADC,eAAe,KAAK,IAAI,GAAG,cAAc;gCAE3C,SAAS,IAAM,cAAc,KAAK,IAAI;0CAEtC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,AAAC,6EAIhB,OAHC,eAAe,KAAK,IAAI,GACpB,+DACA;8DAEH,OAAO,KAAK,IAAI,EAAE,QAAQ,CAAC,GAAG;;;;;;8DAEjC,6LAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;;;;;;;sDAGf,6LAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;;;;;;;+BApBhB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AA8B9B;GA9HwB;KAAA", "debugId": null}}, {"offset": {"line": 2036, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Boldbat/Webs/share_wallet/landio-clone/src/components/SuccessStories.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Image from \"next/image\";\n\nconst caseStudies = [\n  {\n    id: \"max\",\n    name: \"Max's SaaS Revolution\",\n    description: \"<PERSON>, the founder of CloudFlow, implemented AI automation in their processes. This move slashed operational costs by 50% and boosted team productivity by 75%, empowering the company to accelerate growth and expand faster.\",\n    metrics: [\n      { label: \"increase in ROI\", value: \"50\" },\n      { label: \"boost in revenue\", value: \"75\" }\n    ],\n    image: \"https://framerusercontent.com/images/46yWpjkwWiKJojGTr2NKnNPtJ1c.jpg\",\n    quote: \"They took the time to understand our challenges, identified our target audience, and made our brand shine. Their solutions were very effective!\"\n  },\n  {\n    id: \"emily\",\n    name: \"Emily's E-commerce Success\",\n    description: \"<PERSON>, the CEO of BloomTech, transformed their marketing efforts using AI-powered tools. This shift resulted in a 60% increase in ROI and a 45% improvement in customer personalization, leading to a surge in brand loyalty\",\n    metrics: [\n      { label: \"growth in sales\", value: \"60\" },\n      { label: \"rise in engagement\", value: \"45\" }\n    ],\n    image: \"https://framerusercontent.com/images/GuFZFCQnRSOpKJkAPlCkaRUGIjc.png\"\n  },\n  {\n    id: \"sophia\",\n    name: \"<PERSON>'s Retail Breakthrough\",\n    description: \"<PERSON>, the marketing lead at Trendify, used AI-driven analytics to dive deep into customer behavior. The insights led to a 40% increase in engagement and a 30% rise in repeat purchases, creating long-term customer relationships.\",\n    metrics: [\n      { label: \"gain in retention\", value: \"40\" },\n      { label: \"surge in profits\", value: \"30\" }\n    ],\n    image: \"https://framerusercontent.com/images/TXdiLXbrEnofSFENzswfxpdKpc.png\"\n  }\n];\n\nexport default function SuccessStories() {\n  const [activeCase, setActiveCase] = useState(0);\n  const [animatedValues, setAnimatedValues] = useState<{[key: string]: number}>({});\n\n  useEffect(() => {\n    const currentCase = caseStudies[activeCase];\n    const newAnimatedValues: {[key: string]: number} = {};\n    \n    currentCase.metrics.forEach((metric, index) => {\n      const targetValue = parseInt(metric.value);\n      let currentValue = 0;\n      const increment = targetValue / 50; // Animate over 50 steps\n      \n      const timer = setInterval(() => {\n        currentValue += increment;\n        if (currentValue >= targetValue) {\n          currentValue = targetValue;\n          clearInterval(timer);\n        }\n        newAnimatedValues[`${activeCase}-${index}`] = Math.floor(currentValue);\n        setAnimatedValues(prev => ({ ...prev, ...newAnimatedValues }));\n      }, 30);\n    });\n  }, [activeCase]);\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <span className=\"text-primary-600 font-semibold text-sm uppercase tracking-wide mb-4 block\">\n              OUR CLIENTS\n            </span>\n            <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n              Success Stories to Inspire\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Discover how businesses and creators achieve results\n            </p>\n          </div>\n\n          {/* Case Study Carousel */}\n          <div className=\"relative\">\n            <div className=\"bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl overflow-hidden\">\n              <div className=\"grid lg:grid-cols-2 gap-0\">\n                {/* Content */}\n                <div className=\"p-8 lg:p-12 flex flex-col justify-center\">\n                  <h3 className=\"text-3xl font-bold text-gray-900 mb-6\">\n                    {caseStudies[activeCase].name}\n                  </h3>\n                  \n                  <p className=\"text-lg text-gray-600 leading-relaxed mb-8\">\n                    {caseStudies[activeCase].description}\n                  </p>\n\n                  {/* Metrics */}\n                  <div className=\"grid grid-cols-2 gap-6 mb-8\">\n                    {caseStudies[activeCase].metrics.map((metric, index) => (\n                      <div key={index} className=\"text-center\">\n                        <div className=\"text-4xl lg:text-5xl font-bold text-primary-600 mb-2\">\n                          {animatedValues[`${activeCase}-${index}`] || 0}\n                          <span className=\"text-2xl\">%</span>\n                        </div>\n                        <p className=\"text-gray-600 font-medium\">{metric.label}</p>\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* Quote */}\n                  {caseStudies[activeCase].quote && (\n                    <blockquote className=\"text-lg italic text-gray-700 border-l-4 border-primary-500 pl-4\">\n                      \"{caseStudies[activeCase].quote}\"\n                    </blockquote>\n                  )}\n                </div>\n\n                {/* Image */}\n                <div className=\"relative h-64 lg:h-auto\">\n                  <Image\n                    src={caseStudies[activeCase].image}\n                    alt={caseStudies[activeCase].name}\n                    fill\n                    className=\"object-cover\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"></div>\n                </div>\n              </div>\n            </div>\n\n            {/* Navigation Dots */}\n            <div className=\"flex justify-center mt-8 space-x-3\">\n              {caseStudies.map((_, index) => (\n                <button\n                  key={index}\n                  onClick={() => setActiveCase(index)}\n                  className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                    activeCase === index\n                      ? 'bg-primary-500 w-8'\n                      : 'bg-gray-300 hover:bg-gray-400'\n                  }`}\n                  aria-label={`View case study ${index + 1}`}\n                />\n              ))}\n            </div>\n          </div>\n\n          {/* Case Study Cards */}\n          <div className=\"mt-16 grid md:grid-cols-3 gap-8\">\n            {caseStudies.map((caseStudy, index) => (\n              <div\n                key={caseStudy.id}\n                className={`group cursor-pointer transition-all duration-300 ${\n                  activeCase === index ? 'scale-105' : 'hover:scale-102'\n                }`}\n                onClick={() => setActiveCase(index)}\n              >\n                <div className=\"bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-primary-200\">\n                  <div className=\"relative h-48\">\n                    <Image\n                      src={caseStudy.image}\n                      alt={caseStudy.name}\n                      fill\n                      className=\"object-cover\"\n                    />\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent\"></div>\n                    <div className=\"absolute bottom-4 left-4 right-4\">\n                      <h4 className=\"text-white font-bold text-lg\">\n                        {caseStudy.name}\n                      </h4>\n                    </div>\n                  </div>\n                  <div className=\"p-6\">\n                    <p className=\"text-gray-600 text-sm leading-relaxed line-clamp-3\">\n                      {caseStudy.description}\n                    </p>\n                    <div className=\"mt-4 flex justify-between\">\n                      {caseStudy.metrics.map((metric, metricIndex) => (\n                        <div key={metricIndex} className=\"text-center\">\n                          <div className=\"text-2xl font-bold text-primary-600\">\n                            {metric.value}%\n                          </div>\n                          <p className=\"text-xs text-gray-500\">{metric.label}</p>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,SAAS;YACP;gBAAE,OAAO;gBAAmB,OAAO;YAAK;YACxC;gBAAE,OAAO;gBAAoB,OAAO;YAAK;SAC1C;QACD,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,SAAS;YACP;gBAAE,OAAO;gBAAmB,OAAO;YAAK;YACxC;gBAAE,OAAO;gBAAsB,OAAO;YAAK;SAC5C;QACD,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,SAAS;YACP;gBAAE,OAAO;gBAAqB,OAAO;YAAK;YAC1C;gBAAE,OAAO;gBAAoB,OAAO;YAAK;SAC1C;QACD,OAAO;IACT;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAE/E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,cAAc,WAAW,CAAC,WAAW;YAC3C,MAAM,oBAA6C,CAAC;YAEpD,YAAY,OAAO,CAAC,OAAO;4CAAC,CAAC,QAAQ;oBACnC,MAAM,cAAc,SAAS,OAAO,KAAK;oBACzC,IAAI,eAAe;oBACnB,MAAM,YAAY,cAAc,IAAI,wBAAwB;oBAE5D,MAAM,QAAQ;0DAAY;4BACxB,gBAAgB;4BAChB,IAAI,gBAAgB,aAAa;gCAC/B,eAAe;gCACf,cAAc;4BAChB;4BACA,iBAAiB,CAAC,AAAC,GAAgB,OAAd,YAAW,KAAS,OAAN,OAAQ,GAAG,KAAK,KAAK,CAAC;4BACzD;kEAAkB,CAAA,OAAQ,CAAC;wCAAE,GAAG,IAAI;wCAAE,GAAG,iBAAiB;oCAAC,CAAC;;wBAC9D;yDAAG;gBACL;;QACF;mCAAG;QAAC;KAAW;IAEf,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA4E;;;;;;0CAG5F,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,WAAW,CAAC,WAAW,CAAC,IAAI;;;;;;8DAG/B,6LAAC;oDAAE,WAAU;8DACV,WAAW,CAAC,WAAW,CAAC,WAAW;;;;;;8DAItC,6LAAC;oDAAI,WAAU;8DACZ,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC5C,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAU;;wEACZ,cAAc,CAAC,AAAC,GAAgB,OAAd,YAAW,KAAS,OAAN,OAAQ,IAAI;sFAC7C,6LAAC;4EAAK,WAAU;sFAAW;;;;;;;;;;;;8EAE7B,6LAAC;oEAAE,WAAU;8EAA6B,OAAO,KAAK;;;;;;;2DAL9C;;;;;;;;;;gDAWb,WAAW,CAAC,WAAW,CAAC,KAAK,kBAC5B,6LAAC;oDAAW,WAAU;;wDAAkE;wDACpF,WAAW,CAAC,WAAW,CAAC,KAAK;wDAAC;;;;;;;;;;;;;sDAMtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,WAAW,CAAC,WAAW,CAAC,KAAK;oDAClC,KAAK,WAAW,CAAC,WAAW,CAAC,IAAI;oDACjC,IAAI;oDACJ,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMrB,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,GAAG,sBACnB,6LAAC;wCAEC,SAAS,IAAM,cAAc;wCAC7B,WAAW,AAAC,oDAIX,OAHC,eAAe,QACX,uBACA;wCAEN,cAAY,AAAC,mBAA4B,OAAV,QAAQ;uCAPlC;;;;;;;;;;;;;;;;kCAcb,6LAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,WAAW,sBAC3B,6LAAC;gCAEC,WAAW,AAAC,oDAEX,OADC,eAAe,QAAQ,cAAc;gCAEvC,SAAS,IAAM,cAAc;0CAE7B,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,UAAU,KAAK;oDACpB,KAAK,UAAU,IAAI;oDACnB,IAAI;oDACJ,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAG,WAAU;kEACX,UAAU,IAAI;;;;;;;;;;;;;;;;;sDAIrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,UAAU,WAAW;;;;;;8DAExB,6LAAC;oDAAI,WAAU;8DACZ,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BAC9B,6LAAC;4DAAsB,WAAU;;8EAC/B,6LAAC;oEAAI,WAAU;;wEACZ,OAAO,KAAK;wEAAC;;;;;;;8EAEhB,6LAAC;oEAAE,WAAU;8EAAyB,OAAO,KAAK;;;;;;;2DAJ1C;;;;;;;;;;;;;;;;;;;;;;+BA3Bb,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CjC;GA3JwB;KAAA", "debugId": null}}]}