"use client";

import Image from "next/image";

export default function FounderNote() {
  return (
    <section id="founder-note" className="py-20 bg-gradient-to-r from-gray-50 to-blue-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <span className="text-primary-600 font-semibold text-sm uppercase tracking-wide">
              WE ANALYZE YOUR DATA
            </span>
          </div>
          
          <div className="bg-white rounded-2xl shadow-xl p-8 lg:p-12 relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary-100 to-accent-100 rounded-full opacity-50 transform translate-x-16 -translate-y-16"></div>
            
            <div className="grid lg:grid-cols-2 gap-12 items-center relative z-10">
              {/* Quote */}
              <div className="lg:order-1">
                <blockquote className="text-2xl lg:text-3xl font-semibold text-gray-900 leading-relaxed mb-8">
                  "We find what to automate, who your users are & how AI can optimize your workflow. Best part is we also build and launch real solutions."
                </blockquote>
                
                <div className="flex items-center">
                  <div className="w-16 h-16 rounded-full overflow-hidden mr-4 ring-4 ring-primary-100">
                    <Image
                      src="https://framerusercontent.com/images/W7xYkGKzPzvnPv58ZBNzxS3JZI.jpg"
                      alt="Co-founder & AI Strategy Lead"
                      width={64}
                      height={64}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Co-founder & AI Strategy Lead</p>
                    <p className="text-gray-600 text-sm">Landio</p>
                  </div>
                </div>
              </div>
              
              {/* Visual Element */}
              <div className="lg:order-2 flex justify-center">
                <div className="relative">
                  <div className="w-80 h-80 bg-gradient-to-br from-primary-500 to-accent-500 rounded-full opacity-10 animate-pulse"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-64 h-64 bg-gradient-to-br from-primary-400 to-accent-400 rounded-full opacity-20 animate-ping"></div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-48 h-48 bg-gradient-to-br from-primary-300 to-accent-300 rounded-full opacity-30"></div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-32 h-32 bg-gradient-to-br from-primary-500 to-accent-500 rounded-full flex items-center justify-center">
                      <svg className="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
