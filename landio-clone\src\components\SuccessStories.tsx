"use client";

import { useState, useEffect } from "react";
import Image from "next/image";

const caseStudies = [
  {
    id: "max",
    name: "Max's SaaS Revolution",
    description: "<PERSON>, the founder of CloudFlow, implemented AI automation in their processes. This move slashed operational costs by 50% and boosted team productivity by 75%, empowering the company to accelerate growth and expand faster.",
    metrics: [
      { label: "increase in ROI", value: "50" },
      { label: "boost in revenue", value: "75" }
    ],
    image: "https://framerusercontent.com/images/46yWpjkwWiKJojGTr2NKnNPtJ1c.jpg",
    quote: "They took the time to understand our challenges, identified our target audience, and made our brand shine. Their solutions were very effective!"
  },
  {
    id: "emily",
    name: "Emily's E-commerce Success",
    description: "<PERSON>, the CEO of BloomTech, transformed their marketing efforts using AI-powered tools. This shift resulted in a 60% increase in ROI and a 45% improvement in customer personalization, leading to a surge in brand loyalty",
    metrics: [
      { label: "growth in sales", value: "60" },
      { label: "rise in engagement", value: "45" }
    ],
    image: "https://framerusercontent.com/images/GuFZFCQnRSOpKJkAPlCkaRUGIjc.png"
  },
  {
    id: "sophia",
    name: "<PERSON>'s Retail Breakthrough",
    description: "<PERSON>, the marketing lead at Trendify, used AI-driven analytics to dive deep into customer behavior. The insights led to a 40% increase in engagement and a 30% rise in repeat purchases, creating long-term customer relationships.",
    metrics: [
      { label: "gain in retention", value: "40" },
      { label: "surge in profits", value: "30" }
    ],
    image: "https://framerusercontent.com/images/TXdiLXbrEnofSFENzswfxpdKpc.png"
  }
];

export default function SuccessStories() {
  const [activeCase, setActiveCase] = useState(0);
  const [animatedValues, setAnimatedValues] = useState<{[key: string]: number}>({});

  useEffect(() => {
    const currentCase = caseStudies[activeCase];
    const newAnimatedValues: {[key: string]: number} = {};
    
    currentCase.metrics.forEach((metric, index) => {
      const targetValue = parseInt(metric.value);
      let currentValue = 0;
      const increment = targetValue / 50; // Animate over 50 steps
      
      const timer = setInterval(() => {
        currentValue += increment;
        if (currentValue >= targetValue) {
          currentValue = targetValue;
          clearInterval(timer);
        }
        newAnimatedValues[`${activeCase}-${index}`] = Math.floor(currentValue);
        setAnimatedValues(prev => ({ ...prev, ...newAnimatedValues }));
      }, 30);
    });
  }, [activeCase]);

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <span className="text-primary-600 font-semibold text-sm uppercase tracking-wide mb-4 block">
              OUR CLIENTS
            </span>
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Success Stories to Inspire
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover how businesses and creators achieve results
            </p>
          </div>

          {/* Case Study Carousel */}
          <div className="relative">
            <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl overflow-hidden">
              <div className="grid lg:grid-cols-2 gap-0">
                {/* Content */}
                <div className="p-8 lg:p-12 flex flex-col justify-center">
                  <h3 className="text-3xl font-bold text-gray-900 mb-6">
                    {caseStudies[activeCase].name}
                  </h3>
                  
                  <p className="text-lg text-gray-600 leading-relaxed mb-8">
                    {caseStudies[activeCase].description}
                  </p>

                  {/* Metrics */}
                  <div className="grid grid-cols-2 gap-6 mb-8">
                    {caseStudies[activeCase].metrics.map((metric, index) => (
                      <div key={index} className="text-center">
                        <div className="text-4xl lg:text-5xl font-bold text-primary-600 mb-2">
                          {animatedValues[`${activeCase}-${index}`] || 0}
                          <span className="text-2xl">%</span>
                        </div>
                        <p className="text-gray-600 font-medium">{metric.label}</p>
                      </div>
                    ))}
                  </div>

                  {/* Quote */}
                  {caseStudies[activeCase].quote && (
                    <blockquote className="text-lg italic text-gray-700 border-l-4 border-primary-500 pl-4">
                      "{caseStudies[activeCase].quote}"
                    </blockquote>
                  )}
                </div>

                {/* Image */}
                <div className="relative h-64 lg:h-auto">
                  <Image
                    src={caseStudies[activeCase].image}
                    alt={caseStudies[activeCase].name}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                </div>
              </div>
            </div>

            {/* Navigation Dots */}
            <div className="flex justify-center mt-8 space-x-3">
              {caseStudies.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setActiveCase(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    activeCase === index
                      ? 'bg-primary-500 w-8'
                      : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                  aria-label={`View case study ${index + 1}`}
                />
              ))}
            </div>
          </div>

          {/* Case Study Cards */}
          <div className="mt-16 grid md:grid-cols-3 gap-8">
            {caseStudies.map((caseStudy, index) => (
              <div
                key={caseStudy.id}
                className={`group cursor-pointer transition-all duration-300 ${
                  activeCase === index ? 'scale-105' : 'hover:scale-102'
                }`}
                onClick={() => setActiveCase(index)}
              >
                <div className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-primary-200">
                  <div className="relative h-48">
                    <Image
                      src={caseStudy.image}
                      alt={caseStudy.name}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    <div className="absolute bottom-4 left-4 right-4">
                      <h4 className="text-white font-bold text-lg">
                        {caseStudy.name}
                      </h4>
                    </div>
                  </div>
                  <div className="p-6">
                    <p className="text-gray-600 text-sm leading-relaxed line-clamp-3">
                      {caseStudy.description}
                    </p>
                    <div className="mt-4 flex justify-between">
                      {caseStudy.metrics.map((metric, metricIndex) => (
                        <div key={metricIndex} className="text-center">
                          <div className="text-2xl font-bold text-primary-600">
                            {metric.value}%
                          </div>
                          <p className="text-xs text-gray-500">{metric.label}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
