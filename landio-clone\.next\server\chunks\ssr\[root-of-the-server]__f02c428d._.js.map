{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Boldbat/Webs/share_wallet/landio-clone/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function scrollToSection(sectionId: string) {\n  const element = document.getElementById(sectionId);\n  if (element) {\n    element.scrollIntoView({ behavior: \"smooth\" });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,gBAAgB,SAAiB;IAC/C,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,QAAQ,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9C;AACF", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Boldbat/Webs/share_wallet/landio-clone/src/components/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { cn } from \"@/lib/utils\";\nimport type { NavItem } from \"@/types\";\n\nconst navItems: NavItem[] = [\n  { label: \"Services\", href: \"#services\" },\n  { label: \"Process\", href: \"#process\" },\n  { label: \"Pricing\", href: \"#pricing\" },\n  { label: \"Blog\", href: \"/blog\" },\n  { label: \"Contact\", href: \"/contact\" },\n];\n\nexport default function Header() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  const handleNavClick = (href: string) => {\n    if (href.startsWith(\"#\")) {\n      const element = document.querySelector(href);\n      if (element) {\n        element.scrollIntoView({ behavior: \"smooth\" });\n      }\n    }\n    setIsMobileMenuOpen(false);\n  };\n\n  return (\n    <header\n      className={cn(\n        \"fixed top-0 left-0 right-0 z-50 transition-all duration-300\",\n        isScrolled\n          ? \"bg-white/95 backdrop-blur-md shadow-sm border-b border-gray-100\"\n          : \"bg-transparent\"\n      )}\n    >\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-primary-500 to-accent-500 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">L</span>\n            </div>\n            <span className=\"text-xl font-bold text-gray-900\">Landio</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <button\n                key={item.label}\n                onClick={() => handleNavClick(item.href)}\n                className=\"text-gray-600 hover:text-gray-900 font-medium transition-colors duration-200\"\n              >\n                {item.label}\n              </button>\n            ))}\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <Link\n              href=\"https://framer.link/D4dc7gs\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-2.5 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200 btn-hover\"\n            >\n              Get Template\n            </Link>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200\"\n            aria-label=\"Toggle mobile menu\"\n          >\n            <svg\n              className=\"w-6 h-6\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              {isMobileMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"lg:hidden border-t border-gray-100 bg-white/95 backdrop-blur-md\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1\">\n              {navItems.map((item) => (\n                <button\n                  key={item.label}\n                  onClick={() => handleNavClick(item.href)}\n                  className=\"block w-full text-left px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md font-medium transition-colors duration-200\"\n                >\n                  {item.label}\n                </button>\n              ))}\n              <div className=\"pt-4 pb-2\">\n                <Link\n                  href=\"https://framer.link/D4dc7gs\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"block w-full text-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-2.5 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                >\n                  Get Template\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAOA,MAAM,WAAsB;IAC1B;QAAE,OAAO;QAAY,MAAM;IAAY;IACvC;QAAE,OAAO;QAAW,MAAM;IAAW;IACrC;QAAE,OAAO;QAAW,MAAM;IAAW;IACrC;QAAE,OAAO;QAAQ,MAAM;IAAQ;IAC/B;QAAE,OAAO;QAAW,MAAM;IAAW;CACtC;AAEc,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,IAAI,SAAS;gBACX,QAAQ,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAC9C;QACF;QACA,oBAAoB;IACtB;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,oEACA;kBAGN,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;oCAEC,SAAS,IAAM,eAAe,KAAK,IAAI;oCACvC,WAAU;8CAET,KAAK,KAAK;mCAJN,KAAK,KAAK;;;;;;;;;;sCAUrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;0CACX;;;;;;;;;;;sCAMH,8OAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;0CAEP,iCACC,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;yDAGJ,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAQX,kCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;oCAEC,SAAS,IAAM,eAAe,KAAK,IAAI;oCACvC,WAAU;8CAET,KAAK,KAAK;mCAJN,KAAK,KAAK;;;;;0CAOnB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Boldbat/Webs/share_wallet/landio-clone/src/components/Hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { useState, useEffect } from \"react\";\n\nexport default function Hero() {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    setIsVisible(true);\n  }, []);\n\n  return (\n    <section id=\"hero\" className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-50 via-white to-blue-50\">\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-primary-200 to-primary-300 rounded-full opacity-20 animate-pulse\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-accent-200 to-accent-300 rounded-full opacity-20 animate-pulse delay-1000\"></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-primary-100 to-accent-100 rounded-full opacity-30 animate-ping delay-2000\"></div>\n      </div>\n\n      {/* Grid Pattern */}\n      <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <div className=\"text-center max-w-5xl mx-auto\">\n          {/* Badge */}\n          <div className={`inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-primary-100 to-accent-100 text-primary-700 text-sm font-medium mb-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>\n            <span className=\"w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse\"></span>\n            NEW GEN AI AUTOMATION PARTNER\n          </div>\n\n          {/* Main Headline */}\n          <h1 className={`text-4xl sm:text-5xl lg:text-7xl font-bold text-gray-900 mb-6 leading-tight transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n            <span className=\"block\">Automate Smarter.</span>\n            <span className=\"block\">Grow Faster.</span>\n            <span className=\"block gradient-text\">With AI.</span>\n          </h1>\n\n          {/* Subtitle */}\n          <p className={`text-xl sm:text-2xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed transition-all duration-1000 delay-400 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n            AI Automation for Modern Businesses Made Simple\n          </p>\n\n          {/* CTA Button */}\n          <div className={`mb-16 transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n            <Link\n              href=\"https://framer.link/D4dc7gs\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white text-lg font-semibold rounded-xl hover:from-primary-600 hover:to-primary-700 transition-all duration-300 btn-hover shadow-lg hover:shadow-xl\"\n            >\n              Book A Free Call\n              <svg className=\"ml-2 w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n              </svg>\n            </Link>\n          </div>\n\n          {/* Social Media Links */}\n          <div className={`flex items-center justify-center space-x-6 transition-all duration-1000 delay-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n            <Link\n              href=\"https://x.com/home\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center text-gray-600 hover:text-primary-500 hover:shadow-lg transition-all duration-200\"\n              aria-label=\"Twitter\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"/>\n              </svg>\n            </Link>\n            <Link\n              href=\"https://www.instagram.com/\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center text-gray-600 hover:text-primary-500 hover:shadow-lg transition-all duration-200\"\n              aria-label=\"Instagram\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.875-.875 2.026-1.297 3.323-1.297s2.448.422 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.781c-.49 0-.928-.422-.928-.928 0-.49.438-.928.928-.928.49 0 .928.438.928.928 0 .506-.438.928-.928.928zm-3.832 9.244c-1.297 0-2.448-.49-3.323-1.297-.928-.807-1.418-1.958-1.418-3.255s.49-2.448 1.418-3.323c.875-.875 2.026-1.297 3.323-1.297s2.448.422 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.255c-.875.807-2.026 1.297-3.323 1.297z\"/>\n              </svg>\n            </Link>\n            <Link\n              href=\"https://www.facebook.com/\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center text-gray-600 hover:text-primary-500 hover:shadow-lg transition-all duration-200\"\n              aria-label=\"Facebook\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n              </svg>\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <div className=\"w-6 h-10 border-2 border-gray-300 rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-gray-300 rounded-full mt-2 animate-pulse\"></div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;;0BAE3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAW,CAAC,uKAAuK,EAAE,YAAY,8BAA8B,2BAA2B;;8CAC7P,8OAAC;oCAAK,WAAU;;;;;;gCAAgE;;;;;;;sCAKlF,8OAAC;4BAAG,WAAW,CAAC,mHAAmH,EAAE,YAAY,8BAA8B,2BAA2B;;8CACxM,8OAAC;oCAAK,WAAU;8CAAQ;;;;;;8CACxB,8OAAC;oCAAK,WAAU;8CAAQ;;;;;;8CACxB,8OAAC;oCAAK,WAAU;8CAAsB;;;;;;;;;;;;sCAIxC,8OAAC;4BAAE,WAAW,CAAC,iHAAiH,EAAE,YAAY,8BAA8B,2BAA2B;sCAAE;;;;;;sCAKzM,8OAAC;4BAAI,WAAW,CAAC,6CAA6C,EAAE,YAAY,8BAA8B,2BAA2B;sCACnI,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;;oCACX;kDAEC,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAM3E,8OAAC;4BAAI,WAAW,CAAC,kFAAkF,EAAE,YAAY,8BAA8B,2BAA2B;;8CACxK,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAGZ,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAGZ,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 586, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Boldbat/Webs/share_wallet/landio-clone/src/components/FounderNote.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from \"next/image\";\n\nexport default function FounderNote() {\n  return (\n    <section id=\"founder-note\" className=\"py-20 bg-gradient-to-r from-gray-50 to-blue-50\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-12\">\n            <span className=\"text-primary-600 font-semibold text-sm uppercase tracking-wide\">\n              WE ANALYZE YOUR DATA\n            </span>\n          </div>\n          \n          <div className=\"bg-white rounded-2xl shadow-xl p-8 lg:p-12 relative overflow-hidden\">\n            {/* Background decoration */}\n            <div className=\"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary-100 to-accent-100 rounded-full opacity-50 transform translate-x-16 -translate-y-16\"></div>\n            \n            <div className=\"grid lg:grid-cols-2 gap-12 items-center relative z-10\">\n              {/* Quote */}\n              <div className=\"lg:order-1\">\n                <blockquote className=\"text-2xl lg:text-3xl font-semibold text-gray-900 leading-relaxed mb-8\">\n                  \"We find what to automate, who your users are & how AI can optimize your workflow. Best part is we also build and launch real solutions.\"\n                </blockquote>\n                \n                <div className=\"flex items-center\">\n                  <div className=\"w-16 h-16 rounded-full overflow-hidden mr-4 ring-4 ring-primary-100\">\n                    <Image\n                      src=\"https://framerusercontent.com/images/W7xYkGKzPzvnPv58ZBNzxS3JZI.jpg\"\n                      alt=\"Co-founder & AI Strategy Lead\"\n                      width={64}\n                      height={64}\n                      className=\"w-full h-full object-cover\"\n                    />\n                  </div>\n                  <div>\n                    <p className=\"font-semibold text-gray-900\">Co-founder & AI Strategy Lead</p>\n                    <p className=\"text-gray-600 text-sm\">Landio</p>\n                  </div>\n                </div>\n              </div>\n              \n              {/* Visual Element */}\n              <div className=\"lg:order-2 flex justify-center\">\n                <div className=\"relative\">\n                  <div className=\"w-80 h-80 bg-gradient-to-br from-primary-500 to-accent-500 rounded-full opacity-10 animate-pulse\"></div>\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"w-64 h-64 bg-gradient-to-br from-primary-400 to-accent-400 rounded-full opacity-20 animate-ping\"></div>\n                  </div>\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"w-48 h-48 bg-gradient-to-br from-primary-300 to-accent-300 rounded-full opacity-30\"></div>\n                  </div>\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"w-32 h-32 bg-gradient-to-br from-primary-500 to-accent-500 rounded-full flex items-center justify-center\">\n                      <svg className=\"w-16 h-16 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAQ,IAAG;QAAe,WAAU;kBACnC,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAiE;;;;;;;;;;;kCAKnF,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAW,WAAU;0DAAwE;;;;;;0DAI9F,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAI;4DACJ,KAAI;4DACJ,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;;;;;;kEAGd,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAA8B;;;;;;0EAC3C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;kDAM3C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;;;;;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;;;;;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAuB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY/F", "debugId": null}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Boldbat/Webs/share_wallet/landio-clone/src/components/Benefits.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from \"next/image\";\n\nconst benefits = [\n  {\n    title: \"Real-Time Intelligence\",\n    description: \"Access accurate, real-time data to drive smarter decisions\",\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n      </svg>\n    )\n  },\n  {\n    title: \"Measurable Impact\",\n    description: \"Track performance, uncover insights, and achieve data-backed growth\",\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n      </svg>\n    )\n  },\n  {\n    title: \"Seamless Integration\",\n    description: \"Connect tools, teams, and workflows with intelligent automation\",\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\" />\n      </svg>\n    ),\n    image: \"https://framerusercontent.com/images/EdYwMQFSY0q3kCvKPrFpiTV5w.png\"\n  }\n];\n\nexport default function Benefits() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <span className=\"text-primary-600 font-semibold text-sm uppercase tracking-wide mb-4 block\">\n              BENEFITS\n            </span>\n            <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n              Why Choose Us?\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Everything you need to automate, optimize, and scale\n            </p>\n          </div>\n\n          {/* Benefits Grid */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {benefits.map((benefit, index) => (\n              <div\n                key={benefit.title}\n                className=\"group relative bg-white rounded-2xl p-8 border border-gray-100 hover:border-primary-200 transition-all duration-300 card-hover\"\n              >\n                {/* Background decoration */}\n                <div className=\"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary-50 to-accent-50 rounded-full opacity-50 transform translate-x-10 -translate-y-10 group-hover:scale-110 transition-transform duration-300\"></div>\n                \n                <div className=\"relative z-10\">\n                  {/* Icon */}\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform duration-300\">\n                    {benefit.icon}\n                  </div>\n\n                  {/* Content */}\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n                    {benefit.title}\n                  </h3>\n                  <p className=\"text-gray-600 leading-relaxed mb-6\">\n                    {benefit.description}\n                  </p>\n\n                  {/* Image for Seamless Integration */}\n                  {benefit.image && (\n                    <div className=\"mt-6\">\n                      <Image\n                        src={benefit.image}\n                        alt={benefit.title}\n                        width={200}\n                        height={120}\n                        className=\"w-full h-auto rounded-lg opacity-80 group-hover:opacity-100 transition-opacity duration-300\"\n                      />\n                    </div>\n                  )}\n\n                  {/* Hover effect arrow */}\n                  <div className=\"absolute bottom-6 right-6 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0 transition-all duration-300\">\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n                    </svg>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,WAAW;IACf;QACE,OAAO;QACP,aAAa;QACb,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAK,GAAE;;;;;;;;;;;IAG7E;IACA;QACE,OAAO;QACP,aAAa;QACb,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAK,GAAE;;;;;;;;;;;IAG7E;IACA;QACE,OAAO;QACP,aAAa;QACb,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAK,GAAE;;;;;;;;;;;QAG3E,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA4E;;;;;;0CAG5F,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;gCAEC,WAAU;;kDAGV,8OAAC;wCAAI,WAAU;;;;;;kDAEf,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI;;;;;;0DAIf,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;4CAIrB,QAAQ,KAAK,kBACZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,QAAQ,KAAK;oDAClB,KAAK,QAAQ,KAAK;oDAClB,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;0DAMhB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;+BApCtE,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;AA+ClC", "debugId": null}}]}